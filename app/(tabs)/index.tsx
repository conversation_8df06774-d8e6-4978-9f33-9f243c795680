import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Animated,
  Image,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useLocalization } from '../context/LocalizationContext';
import { useSubscription } from '../context/SubscriptionContext';

import {
  getDefaultQuickTopics,
  HealthConversation,
  QuickTopic,
} from '../types/healthConsultation';
import {
  createHealthConversation,
  getHealthConversations,
  getConversationMessageCount,
} from '../services/HealthConsultationSupabase';
import { getUnifiedDeviceId } from '../services/revenueCatService';

export default function HealthConsultationScreen() {
  const { t, language } = useLocalization();
  const router = useRouter();
  const { checkPaywallCondition, subscriptionStatus } = useSubscription();

  // Local state
  const [conversations, setConversations] = useState<HealthConversation[]>([]);
  const [messageCounts, setMessageCounts] = useState<Record<string, number>>(
    {}
  );
  const [isLoading, setIsLoading] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  // Get screen width for responsive design
  const screenWidth = Dimensions.get('window').width;

  // Load conversations on mount
  useEffect(() => {
    loadConversations();

    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const loadConversations = async () => {
    try {
      setIsLoading(true);
      const deviceId = await getUnifiedDeviceId();
      const data = await getHealthConversations(deviceId);
      setConversations(data);

      // Load message counts for each conversation
      const counts: Record<string, number> = {};
      await Promise.all(
        data.map(async (conversation) => {
          const count = await getConversationMessageCount(conversation.id);
          counts[conversation.id] = count;
        })
      );
      setMessageCounts(counts);
    } catch (error) {
      console.error('Failed to load conversations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createConversation = async (
    title?: string,
    topic?: QuickTopic
  ): Promise<HealthConversation> => {
    try {
      const deviceId = await getUnifiedDeviceId();

      const createRequest = {
        title:
          title ||
          (topic
            ? `${t(topic.titleKey)} ${t('healthConsultation.tabTitle')}`
            : t('healthConsultation.tabTitle')),
        deviceId,
        topic: topic?.id,
        language,
      };

      const response = await createHealthConversation(createRequest);
      if (!response || !response.conversation) {
        throw new Error('Failed to create conversation in database');
      }

      // Update local conversations list
      setConversations((prev) => [response.conversation, ...prev]);

      // Initialize message count for new conversation
      setMessageCounts((prev) => ({
        ...prev,
        [response.conversation.id]: 0,
      }));

      return response.conversation;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      throw error;
    }
  };

  const handleStartNewConsultation = async () => {
    try {
      // Check paywall condition for premium users
      if (!subscriptionStatus.isPremium) {
        const shouldShowPaywallModal = await checkPaywallCondition(
          'create_conversation'
        );
        if (shouldShowPaywallModal) {
          router.push('/paywall');
          return;
        }
      }

      const conversation = await createConversation();
      router.push(`/health-chat?conversationId=${conversation.id}`);
    } catch (error) {
      console.error('Failed to create conversation:', error);
    }
  };

  const handleQuickTopicPress = async (
    topic: ReturnType<typeof getDefaultQuickTopics>[0]
  ) => {
    try {
      // Check paywall condition for premium users
      if (!subscriptionStatus.isPremium) {
        const shouldShowPaywallModal = await checkPaywallCondition(
          'create_conversation'
        );
        if (shouldShowPaywallModal) {
          router.push('/paywall');
          return;
        }
      }

      const conversation = await createConversation(undefined, topic);
      router.push(`/health-chat?conversationId=${conversation.id}`);
    } catch (error) {
      console.error('Failed to create conversation with topic:', error);
    }
  };

  const quickTopics = getDefaultQuickTopics(t).map((topic) => ({
    ...topic,
    title: t(topic.titleKey),
  }));

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Welcome Section */}
        <Animated.View
          style={[
            styles.welcomeSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Image
            source={require('../../assets/images/doctorWithoutBg.png')}
            style={[styles.doctorIcon, { width: screenWidth }]}
            resizeMode="contain"
          />
          <Text style={styles.welcomeMessage}>
            {t('healthConsultation.welcomeMessage')}
          </Text>
        </Animated.View>

        {/* Start New Consultation Button */}
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }}
        >
          <TouchableOpacity
            style={styles.startButton}
            onPress={handleStartNewConsultation}
            activeOpacity={0.8}
          >
            <Ionicons name="add-circle-outline" size={24} color="white" />
            <Text style={styles.startButtonText}>
              {t('healthConsultation.startNewConsultation')}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Quick Topics */}
        <Animated.View
          style={[
            styles.quickTopicsSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Text style={styles.sectionTitle}>
            {t('healthConsultation.quickTopicsTitle')}
          </Text>
          <View style={styles.topicsGrid}>
            {quickTopics.map((topic) => (
              <TouchableOpacity
                key={topic.id}
                style={[styles.topicCard, { borderLeftColor: topic.color }]}
                onPress={() => handleQuickTopicPress(topic)}
                activeOpacity={0.7}
              >
                <View
                  style={[styles.topicIcon, { backgroundColor: topic.color }]}
                >
                  <Ionicons name={topic.icon as any} size={24} color="white" />
                </View>
                <Text style={styles.topicTitle}>{topic.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Recent Conversations Section */}
        <Animated.View
          style={[
            styles.recentSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Text style={styles.sectionTitle}>
            {t('healthConsultation.recentConversations')}
          </Text>
          {conversations.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="chatbubbles-outline" size={48} color="#9CA3AF" />
              <Text style={styles.emptyStateText}>
                {t('healthConsultation.noConversations')}
              </Text>
              <Text style={styles.emptyStateSubtext}>
                {t('healthConsultation.startFirstConsultation')}
              </Text>
            </View>
          ) : (
            <View style={styles.conversationsList}>
              {conversations.slice(0, 3).map((conversation) => (
                <TouchableOpacity
                  key={conversation.id}
                  style={styles.conversationCard}
                  onPress={() => {
                    router.push(
                      `/health-chat?conversationId=${conversation.id}`
                    );
                  }}
                  onLongPress={() => {
                    router.push(
                      `/health-conversation-detail?conversationId=${conversation.id}`
                    );
                  }}
                  activeOpacity={0.7}
                >
                  <View style={styles.conversationHeader}>
                    <Text style={styles.conversationTitle} numberOfLines={1}>
                      {conversation.title}
                    </Text>
                    <Text style={styles.conversationDate}>
                      {new Date(conversation.updatedAt).toLocaleDateString()}
                    </Text>
                  </View>
                  {conversation.lastMessage && (
                    <Text style={styles.conversationPreview} numberOfLines={2}>
                      {conversation.lastMessage}
                    </Text>
                  )}
                  <View style={styles.conversationMeta}>
                    <Text style={styles.messageCount}>
                      {messageCounts[conversation.id] || 0} messages
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
              {conversations.length > 3 && (
                <TouchableOpacity
                  style={styles.viewAllButton}
                  onPress={() => router.push('/health-history')}
                >
                  <Text style={styles.viewAllText}>
                    View all {conversations.length} conversations
                  </Text>
                  <Ionicons name="chevron-forward" size={16} color="#2196F3" />
                </TouchableOpacity>
              )}
            </View>
          )}
        </Animated.View>

        {/* Medical Disclaimer */}
        <Animated.View
          style={[
            styles.disclaimerSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.disclaimerHeader}>
            <Ionicons
              name="information-circle-outline"
              size={20}
              color="#FF9800"
            />
            <Text style={styles.disclaimerTitle}>
              {t('healthConsultation.disclaimer.title')}
            </Text>
          </View>
          <Text style={styles.disclaimerText}>
            {t('healthConsultation.disclaimer.message')}
          </Text>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  welcomeSection: {
    alignItems: 'center',

    top: -50,
    marginBottom: -45,
  },
  welcomeIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  doctorIcon: {
    height: undefined, // Let aspect ratio determine height
    aspectRatio: 1, // Assuming doctor.png is square, adjust if needed
    maxHeight: 400, // Prevent it from being too tall
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 8,
    top: -15,
  },
  welcomeMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    top: -15,
  },
  startButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 30,
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  startButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  quickTopicsSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  topicsGrid: {
    gap: 12,
  },
  topicCard: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  topicIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  topicTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
    flex: 1,
  },
  recentSection: {
    marginBottom: 30,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
  },
  disclaimerSection: {
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 152, 0, 0.2)',
  },
  disclaimerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  disclaimerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FF9800',
    marginLeft: 6,
  },
  disclaimerText: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
  },
  conversationsList: {
    gap: 12,
  },
  conversationCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  conversationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    flex: 1,
    marginRight: 8,
  },
  conversationDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  conversationPreview: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  conversationMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageCount: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
  },
  viewAllText: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '500',
    marginRight: 4,
  },
});
