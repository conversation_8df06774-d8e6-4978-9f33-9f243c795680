import React, { useEffect, useRef, useState } from 'react';
import {
  StyleSheet,
  Alert,
  Animated,
  RefreshControl,
  FlatList,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { MessageBubble } from './MessageBubble';
import { TypingIndicator } from './TypingIndicator';
import { ChatInput } from './ChatInput';
import { useLocalization } from '../../context/LocalizationContext';
import { useSubscription } from '../../context/SubscriptionContext';
import { useRouter } from 'expo-router';

import { HealthMessage } from '../../types/healthConsultation';
import { getHealthMessages } from '../../services/HealthConsultationSupabase';
import { healthConsultationRealtime } from '../../services/HealthConsultationRealtime';
import { getUnifiedDeviceId } from '../../services/revenueCatService';
import { supabase } from '../../lib/supabase';

interface ChatScreenProps {
  conversationId: string;
  onBack?: () => void;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({
  conversationId,
  onBack,
}) => {
  const { t } = useLocalization();
  const router = useRouter();
  const { checkPaywallCondition, trackMessageSent, subscriptionStatus } =
    useSubscription();

  // 🎯 SIMPLE LOCAL STATE: No Context needed
  const [messages, setMessages] = useState<HealthMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  const flatListRef = useRef<FlatList>(null);
  const [refreshing, setRefreshing] = useState(false);
  const messageAnimations = useRef<Map<string, Animated.Value>>(new Map());

  // 🎯 SIMPLE: Load messages and setup real-time subscription
  useEffect(() => {
    if (!conversationId) return;

    console.log(
      '🔄 ChatScreen: Loading messages and setting up real-time for:',
      conversationId
    );

    // Load initial messages
    const loadInitialMessages = async () => {
      try {
        setIsLoading(true);
        const loadedMessages = await getHealthMessages(conversationId);
        setMessages(loadedMessages);

        // Check for pending messages
        const hasPending = loadedMessages.some(
          (msg) => msg.role === 'user' && msg.aiResponseStatus === 'pending'
        );
        setIsTyping(hasPending);

        console.log(
          '✅ Loaded',
          loadedMessages.length,
          'messages, pending:',
          hasPending
        );
      } catch (error) {
        console.error('❌ Error loading messages:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Setup real-time subscription
    const subscription = healthConsultationRealtime.subscribeToMessages(
      conversationId,
      (newMessage) => {
        // Only handle AI messages from real-time
        // User messages are handled optimistically
        if (newMessage.role === 'user') {
          // Update temporary user message status to 'completed' (çift tik)
          setMessages((prev) => {
            const tempIndex = prev.findIndex(
              (msg) =>
                msg.id.startsWith('temp-') &&
                msg.content === newMessage.content &&
                msg.role === 'user'
            );
            if (tempIndex >= 0) {
              const updated = [...prev];
              updated[tempIndex] = {
                ...updated[tempIndex],
                aiResponseStatus: 'completed', // Çift tik - database'e kaydedildi
              };
              return updated;
            }
            return prev; // No change if temp message not found
          });
          return; // Don't add user message to list
        }

        // Handle AI messages normally
        setMessages((prev) => {
          // Check for exact ID match
          const exactIndex = prev.findIndex((msg) => msg.id === newMessage.id);
          if (exactIndex >= 0) {
            const updated = [...prev];
            updated[exactIndex] = newMessage;
            return updated;
          }
          // Add new AI message
          return [...prev, newMessage];
        });

        // Clear typing if AI response
        if (newMessage.role === 'assistant') {
          console.log('🤖 AI response received, clearing typing');
          setIsTyping(false);
        }
      },
      (error) => {
        console.error('❌ Real-time subscription error:', error);
      }
    );

    loadInitialMessages();

    // Cleanup subscription
    return () => {
      console.log('🧹 Cleaning up real-time subscription for:', conversationId);
      healthConsultationRealtime.unsubscribeFromMessages(conversationId);
    };
  }, [conversationId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0 && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  // Create animation for new messages
  const createMessageAnimation = (messageId: string) => {
    if (!messageAnimations.current.has(messageId)) {
      const animValue = new Animated.Value(0);
      messageAnimations.current.set(messageId, animValue);

      Animated.timing(animValue, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      return animValue;
    }
    return messageAnimations.current.get(messageId);
  };

  // 🎯 SUPABASE-FIRST: Call Supabase function to handle everything
  const handleSendMessage = async (content: string) => {
    try {
      // Check paywall condition for premium users
      if (!subscriptionStatus.isPremium) {
        const shouldShowPaywallModal = await checkPaywallCondition(
          'send_message',
          conversationId
        );
        if (shouldShowPaywallModal) {
          router.push('/paywall');
          return;
        }
      }

      console.log('🚀 Calling Supabase function to handle message:', content);
      console.log('🔍 Current messages count before send:', messages.length);
      setIsTyping(true); // Show typing immediately

      // Get unified device ID for RevenueCat integration
      const deviceId = await getUnifiedDeviceId();
      console.log('🔑 Using device ID:', deviceId);

      // 🎯 IMMEDIATE UI UPDATE: Add user message to local state immediately with pending status
      const tempUserMessage: HealthMessage = {
        id: `temp-${Date.now()}`, // Temporary ID
        conversationId,
        role: 'user',
        content,
        messageType: 'text',
        timestamp: Date.now(),
        aiResponseStatus: 'pending', // Tek tik
      };

      // Add to local state immediately for instant UI feedback
      setMessages((prevMessages) => [...prevMessages, tempUserMessage]);

      // Call Supabase function - it will handle everything
      const response = await supabase.functions.invoke('send-health-message', {
        body: {
          conversationId,
          content,
          messageType: 'text',
          language: 'tr',
          deviceId, // Add device ID for RevenueCat integration
        },
      });

      console.log('🔍 Full Supabase function response:', response);

      if (response.error) {
        console.error('🚨 Supabase function error details:', {
          error: response.error,
          message: response.error.message,
          details: response.error.details,
          data: response.data,
        });
        throw response.error;
      }

      const { data } = response;

      console.log('✅ Supabase function completed:', data);
      console.log('⏳ Real-time will update UI automatically...');

      // Track message sent for usage limits (only for non-premium users)
      if (!subscriptionStatus.isPremium) {
        await trackMessageSent(conversationId);
      }
    } catch (error) {
      // Only show alert for actual AI service failures
      // Database-first architecture handles most errors gracefully via real-time sync
      console.error('ChatScreen: Error sending message:', error);
      console.error('ChatScreen: Error details:', {
        name: (error as any)?.name,
        message: (error as any)?.message,
        stack: (error as any)?.stack,
      });

      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';

      // Only show alert for critical errors (not database save failures)
      if (
        errorMessage.includes('AI service') ||
        errorMessage.includes('network') ||
        errorMessage.includes('API')
      ) {
        Alert.alert(
          t('common.error'),
          t('healthConsultation.sendMessageError'),
          [{ text: t('common.ok') }]
        );
      } else {
        // For other errors (like title generation), just log - real-time sync will handle
        console.warn(
          'ChatScreen: Non-critical error, relying on real-time sync:',
          errorMessage
        );
      }
    }
  };

  const handleRetryMessage = async (message: HealthMessage) => {
    try {
      await handleSendMessage(message.content);
    } catch (error) {
      Alert.alert(
        t('common.error'),
        t('healthConsultation.retryMessageError'),
        [{ text: t('common.ok') }]
      );
    }
  };

  const handleRefresh = async () => {
    if (conversationId) {
      setRefreshing(true);
      try {
        // Simple refresh: reload messages from DB
        const loadedMessages = await getHealthMessages(conversationId);
        setMessages(loadedMessages);
      } catch (error) {
        console.error('Error refreshing messages:', error);
      } finally {
        setRefreshing(false);
      }
    }
  };

  // Simplified: No pagination for now
  const handleLoadMore = () => {
    console.log('Load more not implemented in simple version');
  };

  const renderMessage = ({
    item,
    index,
  }: {
    item: HealthMessage;
    index: number;
  }) => {
    const isUser = item.role === 'user';
    const showTimestamp =
      index === 0 ||
      (messages[index - 1] &&
        Math.abs(item.timestamp - messages[index - 1].timestamp) > 300000); // 5 minutes

    const animValue = createMessageAnimation(item.id);

    return (
      <MessageBubble
        message={item}
        isUser={isUser}
        showTimestamp={showTimestamp}
        onRetry={() => handleRetryMessage(item)}
        animatedValue={animValue}
      />
    );
  };

  const renderFooter = () => {
    if (isTyping) {
      return <TypingIndicator visible={true} />;
    }
    return null;
  };

  const keyExtractor = (item: HealthMessage) => item.id;

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
    >
      {/* Message List Area - This will shrink when keyboard appears */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={keyExtractor}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        inverted={false}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor="#2196F3"
            colors={['#2196F3']}
          />
        }
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10,
        }}
        keyboardShouldPersistTaps="handled"
      />

      {/* Input Container - This stays at bottom */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isLoading || isTyping}
        placeholder={t('healthConsultation.typeMessage')}
      />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },

  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
    flexGrow: 1,
    justifyContent: 'flex-end',
  },
});
