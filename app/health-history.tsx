import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  Dimensions,
  Platform,
} from 'react-native';
import { MenuView } from '@react-native-menu/menu';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import Animated, {
  FadeInDown,
  FadeInRight,
  SlideInRight,
  SlideOutRight,
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import {
  PanGestureHandler,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';

import { useLocalization } from './context/LocalizationContext';
import { HealthConversation } from './types/healthConsultation';
import {
  ConversationFiltersModal,
  SortOption,
  FilterOption,
  FilterSortOptions,
  DateRange,
} from './components/health/ConversationFiltersModal';
import {
  getHealthConversations,
  deleteHealthConversation,
  updateHealthConversation,
  getConversationMessageCount,
} from './services/HealthConsultationSupabase';

const SCREEN_WIDTH = Dimensions.get('window').width;
const SWIPE_THRESHOLD = SCREEN_WIDTH * 0.25; // 25% of screen width
const DELETE_THRESHOLD = SCREEN_WIDTH * 0.5; // 50% of screen width

interface SwipeableConversationItemProps {
  item: HealthConversation;
  index: number;
  isSelected: boolean;
  selectionMode: boolean;
  onPress: (item: HealthConversation) => void;
  onLongPress: (item: HealthConversation) => void;
  onDelete: (item: HealthConversation) => void;
  onArchive: (item: HealthConversation) => void;
  t: (key: string) => string;
  messageCounts: Record<string, number>;
}

const SwipeableConversationItem: React.FC<SwipeableConversationItemProps> = ({
  item,
  index,
  isSelected,
  selectionMode,
  onPress,
  onLongPress,
  onDelete,
  onArchive,
  t,
  messageCounts,
}) => {
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);
  const scale = useSharedValue(1);

  const gestureHandler =
    useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
      onStart: () => {
        scale.value = withSpring(0.95);
      },
      onActive: (event) => {
        // Only allow left swipe (negative translation)
        if (event.translationX < 0) {
          translateX.value = Math.max(event.translationX, -DELETE_THRESHOLD);
        }
      },
      onEnd: (event) => {
        scale.value = withSpring(1);

        const shouldDelete = event.translationX < -DELETE_THRESHOLD;
        const shouldShowActions = event.translationX < -SWIPE_THRESHOLD;

        if (shouldDelete) {
          // Animate out and delete
          translateX.value = withTiming(-SCREEN_WIDTH, { duration: 300 });
          opacity.value = withTiming(0, { duration: 300 }, () => {
            runOnJS(onDelete)(item);
          });
        } else if (shouldShowActions) {
          // Show action buttons
          translateX.value = withSpring(-120);
        } else {
          // Snap back
          translateX.value = withSpring(0);
        }
      },
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }, { scale: scale.value }],
    opacity: opacity.value,
  }));

  const actionButtonsStyle = useAnimatedStyle(() => ({
    opacity: translateX.value < -SWIPE_THRESHOLD ? 1 : 0,
    transform: [
      {
        translateX: Math.min(0, translateX.value + 120),
      },
    ],
  }));

  return (
    <Animated.View
      entering={FadeInDown.delay(index * 50)}
      exiting={SlideOutRight}
      style={styles.swipeableContainer}
    >
      {/* Action Buttons Background */}
      <Animated.View style={[styles.actionButtons, actionButtonsStyle]}>
        <TouchableOpacity
          style={[styles.swipeActionButton, styles.swipeArchiveButton]}
          onPress={() => {
            translateX.value = withSpring(0);
            onArchive(item);
          }}
        >
          <Ionicons
            name={item.isArchived ? 'archive-outline' : 'archive'}
            size={20}
            color="white"
          />
          <Text style={styles.swipeActionButtonText}>
            {item.isArchived
              ? t('healthConsultation.history.active')
              : t('healthConsultation.history.archived')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.swipeActionButton, styles.swipeDeleteButton]}
          onPress={() => {
            translateX.value = withTiming(-SCREEN_WIDTH, { duration: 300 });
            opacity.value = withTiming(0, { duration: 300 }, () => {
              runOnJS(onDelete)(item);
            });
          }}
        >
          <Ionicons name="trash" size={20} color="white" />
          <Text style={styles.swipeActionButtonText}>Delete</Text>
        </TouchableOpacity>
      </Animated.View>

      {/* Main Content */}
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={animatedStyle}>
          <MenuView
            title={item.title}
            onPressAction={({ nativeEvent }) => {
              switch (nativeEvent.event) {
                case 'open-chat':
                  router.push(`/health-chat?conversationId=${item.id}`);
                  break;
                case 'view-details':
                  router.push(
                    `/health-conversation-detail?conversationId=${item.id}`
                  );
                  break;
                case 'toggle-archive':
                  onArchive(item);
                  break;
                case 'delete':
                  onDelete(item);
                  break;
              }
            }}
            actions={[
              {
                id: 'open-chat',
                title: t('healthConsultation.detail.openChat'),
                image: Platform.OS === 'ios' ? 'message' : undefined,
              },
              {
                id: 'view-details',
                title: t('healthConsultation.detail.title'),
                image: Platform.OS === 'ios' ? 'info.circle' : undefined,
              },
              {
                id: 'toggle-archive',
                title: item.isArchived
                  ? t('healthConsultation.history.active')
                  : t('healthConsultation.history.archived'),
                image: Platform.OS === 'ios' ? 'archivebox' : undefined,
              },
              {
                id: 'delete',
                title: t('common.delete'),
                attributes: {
                  destructive: true,
                },
                image: Platform.OS === 'ios' ? 'trash' : undefined,
              },
            ]}
            shouldOpenOnLongPress={true}
          >
            <TouchableOpacity
              style={[
                styles.conversationCard,
                isSelected && styles.selectedCard,
                item.isArchived && styles.archivedCard,
              ]}
              onPress={() => onPress(item)}
              onLongPress={() => onLongPress(item)}
              activeOpacity={0.7}
            >
              {selectionMode && (
                <Animated.View
                  entering={SlideInRight}
                  exiting={SlideOutRight}
                  style={styles.selectionIndicator}
                >
                  <Ionicons
                    name={isSelected ? 'checkmark-circle' : 'ellipse-outline'}
                    size={24}
                    color={isSelected ? '#4CAF50' : '#ccc'}
                  />
                </Animated.View>
              )}

              <View style={styles.conversationContent}>
                <View style={styles.conversationHeader}>
                  <Text style={styles.conversationTitle} numberOfLines={1}>
                    {item.title}
                  </Text>
                  <View style={styles.conversationMeta}>
                    {item.isArchived && (
                      <Ionicons
                        name="archive"
                        size={14}
                        color="#FF9800"
                        style={styles.archiveIcon}
                      />
                    )}
                    <Text style={styles.conversationDate}>
                      {new Date(item.updatedAt).toLocaleDateString()}
                    </Text>
                  </View>
                </View>

                {item.lastMessage && (
                  <Text style={styles.conversationPreview} numberOfLines={2}>
                    {item.lastMessage}
                  </Text>
                )}

                <View style={styles.conversationFooter}>
                  <Text style={styles.messageCount}>
                    {messageCounts[item.id] || 0}{' '}
                    {t('healthConsultation.history.messages')}
                  </Text>
                  <Text style={styles.conversationTime}>
                    {new Date(item.updatedAt).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          </MenuView>
        </Animated.View>
      </PanGestureHandler>
    </Animated.View>
  );
};

export default function HealthHistoryScreen() {
  const { t } = useLocalization();

  // Local state for conversations
  const [conversations, setConversations] = useState<HealthConversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [filterOptions, setFilterOptions] = useState<FilterSortOptions>({
    sortBy: 'date',
    filterBy: 'all',
    dateRange: { startDate: null, endDate: null },
    messageCountRange: { min: null, max: null },
  });
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedConversations, setSelectedConversations] = useState<string[]>(
    []
  );
  const [selectionMode, setSelectionMode] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showSearchSuggestions, setShowSearchSuggestions] = useState(false);
  const [messageCounts, setMessageCounts] = useState<Record<string, number>>(
    {}
  );

  // Load conversations from Supabase
  const loadConversations = async () => {
    try {
      setLoading(true);
      const deviceId =
        await require('./services/revenueCatService').getUnifiedDeviceId();
      const data = await getHealthConversations(deviceId);
      setConversations(data);

      // Load message counts for each conversation
      const counts: Record<string, number> = {};
      await Promise.all(
        data.map(async (conversation) => {
          const count = await getConversationMessageCount(conversation.id);
          counts[conversation.id] = count;
        })
      );
      setMessageCounts(counts);
    } catch (error) {
      console.error('Error loading conversations:', error);
      Alert.alert(t('common.error'), t('healthConsultation.history.loadError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConversations();
  }, []);

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);

      // Add to search history if query is not empty and not already in history
      if (searchQuery.trim() && !searchHistory.includes(searchQuery.trim())) {
        setSearchHistory((prev) => [searchQuery.trim(), ...prev.slice(0, 4)]); // Keep last 5 searches
      }
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchQuery, searchHistory]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await loadConversations();
    } catch (error) {
      console.error('Error refreshing conversations:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Filter and sort conversations
  const filteredAndSortedConversations = useMemo(() => {
    let filtered = conversations;

    // Apply search filter (using debounced query for better performance)
    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase();
      filtered = filtered.filter((conv) => {
        // Search in title
        if (conv.title.toLowerCase().includes(query)) {
          return true;
        }

        // Search in last message
        if (conv.lastMessage?.toLowerCase().includes(query)) {
          return true;
        }

        // Search in metadata tags
        if (
          conv.metadata?.tags?.some((tag) => tag.toLowerCase().includes(query))
        ) {
          return true;
        }

        // Search in topic
        if (conv.metadata?.topic?.toLowerCase().includes(query)) {
          return true;
        }

        return false;
      });
    }

    // Apply status filter
    if (filterOptions.filterBy !== 'all') {
      filtered = filtered.filter((conv) => {
        if (filterOptions.filterBy === 'archived') {
          return conv.isArchived === true;
        }
        return conv.isArchived !== true; // active
      });
    }

    // Apply date range filter
    if (filterOptions.dateRange.startDate || filterOptions.dateRange.endDate) {
      filtered = filtered.filter((conv) => {
        const convDate = new Date(conv.updatedAt);
        const startDate = filterOptions.dateRange.startDate;
        const endDate = filterOptions.dateRange.endDate;

        if (startDate && convDate < startDate) return false;
        if (endDate && convDate > endDate) return false;
        return true;
      });
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (filterOptions.sortBy) {
        case 'date':
          return (
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          );
        case 'created':
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case 'messages':
          return (b.messageCount || 0) - (a.messageCount || 0);
        case 'title':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

    return sorted;
  }, [conversations, debouncedSearchQuery, filterOptions]);

  const handleConversationPress = (conversation: HealthConversation) => {
    if (selectionMode) {
      toggleSelection(conversation.id);
    } else {
      // Directly open the conversation chat
      router.push(`/health-chat?conversationId=${conversation.id}`);
    }
  };

  const handleLongPress = (conversation: HealthConversation) => {
    if (selectionMode) {
      toggleSelection(conversation.id);
    } else {
      // For Android, fallback to Alert
      if (Platform.OS === 'android') {
        Alert.alert(
          conversation.title,
          t('healthConsultation.history.selectAction'),
          [
            {
              text: t('common.cancel'),
              style: 'cancel',
            },
            {
              text: t('healthConsultation.detail.title'),
              onPress: () =>
                router.push(
                  `/health-conversation-detail?conversationId=${conversation.id}`
                ),
            },
            {
              text: t('healthConsultation.detail.openChat'),
              onPress: () =>
                router.push(`/health-chat?conversationId=${conversation.id}`),
            },
            {
              text: conversation.isArchived
                ? t('healthConsultation.history.active')
                : t('healthConsultation.history.archived'),
              onPress: () => handleArchiveConversation(conversation),
            },
            {
              text: t('common.delete'),
              style: 'destructive',
              onPress: () => handleDeleteConversation(conversation),
            },
          ]
        );
      }
      // For iOS, the context menu is handled by MenuView wrapper
    }
  };

  const handleDeleteConversation = useCallback(
    async (conversation: HealthConversation) => {
      Alert.alert(
        t('healthConsultation.history.deleteTitle'),
        t('healthConsultation.history.deleteMessage'),
        [
          {
            text: t('common.cancel'),
            style: 'cancel',
          },
          {
            text: t('common.delete'),
            style: 'destructive',
            onPress: async () => {
              try {
                await deleteHealthConversation(conversation.id);
                // Remove from local state
                setConversations((prev) =>
                  prev.filter((c) => c.id !== conversation.id)
                );
              } catch (error) {
                console.error('Error deleting conversation:', error);
                Alert.alert(
                  t('common.error'),
                  t('healthConsultation.history.deleteError')
                );
              }
            },
          },
        ]
      );
    },
    [t]
  );

  const handleArchiveConversation = useCallback(
    async (conversation: HealthConversation) => {
      try {
        const newArchivedStatus = !conversation.isArchived;
        await updateHealthConversation(conversation.id, {
          isArchived: newArchivedStatus,
        });

        // Update local state
        setConversations((prev) =>
          prev.map((c) =>
            c.id === conversation.id
              ? { ...c, isArchived: newArchivedStatus }
              : c
          )
        );
      } catch (error) {
        console.error('Error archiving/unarchiving conversation:', error);
        Alert.alert(
          t('common.error'),
          t('healthConsultation.history.archiveError')
        );
      }
    },
    [t]
  );

  const toggleSelection = (conversationId: string) => {
    setSelectedConversations((prev) =>
      prev.includes(conversationId)
        ? prev.filter((id) => id !== conversationId)
        : [...prev, conversationId]
    );
  };

  const exitSelectionMode = () => {
    setSelectionMode(false);
    setSelectedConversations([]);
  };

  const handleBulkDelete = () => {
    Alert.alert(
      t('healthConsultation.history.deleteConfirmTitle'),
      t('healthConsultation.history.deleteConfirmMessage', {
        count: selectedConversations.length,
      }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              // Delete all selected conversations
              for (const id of selectedConversations) {
                await deleteHealthConversation(id);
              }

              // Remove from local state
              setConversations((prev) =>
                prev.filter((c) => !selectedConversations.includes(c.id))
              );

              exitSelectionMode();
            } catch (error) {
              console.error('Error bulk deleting conversations:', error);
              Alert.alert(
                t('common.error'),
                t('healthConsultation.history.deleteError')
              );
            }
          },
        },
      ]
    );
  };

  const renderConversationItem = ({
    item,
    index,
  }: {
    item: HealthConversation;
    index: number;
  }) => {
    const isSelected = selectedConversations.includes(item.id);

    return (
      <SwipeableConversationItem
        item={item}
        index={index}
        isSelected={isSelected}
        selectionMode={selectionMode}
        onPress={handleConversationPress}
        onLongPress={handleLongPress}
        onDelete={handleDeleteConversation}
        onArchive={handleArchiveConversation}
        t={t}
        messageCounts={messageCounts}
      />
    );
  };

  const renderEmptyState = () => (
    <Animated.View entering={FadeInDown} style={styles.emptyState}>
      <Ionicons name="chatbubbles-outline" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>
        {searchQuery
          ? t('healthConsultation.history.noSearchResults')
          : t('healthConsultation.history.noConversations')}
      </Text>
      <Text style={styles.emptySubtitle}>
        {searchQuery
          ? t('healthConsultation.history.tryDifferentSearch')
          : t('healthConsultation.history.startFirstConsultation')}
      </Text>
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: t('healthConsultation.history.title'),
          headerRight: () =>
            selectionMode ? (
              <TouchableOpacity
                onPress={exitSelectionMode}
                style={styles.headerButton}
              >
                <Text style={styles.headerButtonText}>
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={() => setShowFiltersModal(true)}
                style={styles.headerButton}
              >
                <Ionicons name="options-outline" size={24} color="#2196F3" />
              </TouchableOpacity>
            ),
        }}
      />

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons
          name="search"
          size={20}
          color="#666"
          style={styles.searchIcon}
        />
        <TextInput
          style={styles.searchInput}
          placeholder={t('healthConsultation.history.searchPlaceholder')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onFocus={() => setShowSearchSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSearchSuggestions(false), 200)}
          placeholderTextColor="#999"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            onPress={() => setSearchQuery('')}
            style={styles.clearButton}
          >
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Search Suggestions */}
      {showSearchSuggestions && searchHistory.length > 0 && (
        <Animated.View
          entering={FadeInDown}
          style={styles.suggestionsContainer}
        >
          <Text style={styles.suggestionsTitle}>
            {t('healthConsultation.history.recentSearches')}
          </Text>
          {searchHistory.map((historyItem, index) => (
            <TouchableOpacity
              key={index}
              style={styles.suggestionItem}
              onPress={() => {
                setSearchQuery(historyItem);
                setShowSearchSuggestions(false);
              }}
            >
              <Ionicons name="time-outline" size={16} color="#666" />
              <Text style={styles.suggestionText}>{historyItem}</Text>
              <TouchableOpacity
                onPress={() => {
                  setSearchHistory((prev) =>
                    prev.filter((_, i) => i !== index)
                  );
                }}
                style={styles.removeSuggestion}
              >
                <Ionicons name="close" size={14} color="#999" />
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
        </Animated.View>
      )}

      {/* Active Filters Indicator */}
      {(filterOptions.filterBy !== 'all' ||
        filterOptions.sortBy !== 'date' ||
        filterOptions.dateRange.startDate ||
        filterOptions.dateRange.endDate) && (
        <Animated.View
          entering={FadeInDown}
          style={styles.activeFiltersContainer}
        >
          <View style={styles.activeFiltersContent}>
            <Ionicons name="funnel" size={16} color="#2196F3" />
            <Text style={styles.activeFiltersText}>
              {t('healthConsultation.filters.title')}
            </Text>
            <TouchableOpacity
              onPress={() => {
                setFilterOptions({
                  sortBy: 'date',
                  filterBy: 'all',
                  dateRange: { startDate: null, endDate: null },
                  messageCountRange: { min: null, max: null },
                });
              }}
              style={styles.clearFiltersButton}
            >
              <Ionicons name="close-circle" size={16} color="#666" />
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}

      {/* Selection Mode Actions */}
      {selectionMode && (
        <Animated.View entering={SlideInRight} style={styles.selectionActions}>
          <Text style={styles.selectionCount}>
            {selectedConversations.length}{' '}
            {t('healthConsultation.history.selected')}
          </Text>
          <View style={styles.selectionButtons}>
            <TouchableOpacity
              onPress={handleBulkDelete}
              style={[styles.actionButton, styles.deleteButton]}
              disabled={selectedConversations.length === 0}
            >
              <Ionicons name="trash-outline" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>{t('common.delete')}</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}

      {/* Conversations List */}
      <FlatList
        data={filteredAndSortedConversations}
        renderItem={renderConversationItem}
        keyExtractor={(item) => item.id}
        style={styles.flatList}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Filters Modal */}
      <ConversationFiltersModal
        visible={showFiltersModal}
        onClose={() => setShowFiltersModal(false)}
        onApply={(newOptions) => {
          setFilterOptions(newOptions);
          setShowFiltersModal(false);
        }}
        currentOptions={filterOptions}
        t={t}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  headerButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  headerButtonText: {
    color: '#2196F3',
    fontSize: 16,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    padding: 4,
  },
  suggestionsContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: -8,
    borderRadius: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  suggestionsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
    paddingHorizontal: 16,
    paddingBottom: 8,
    textTransform: 'uppercase',
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  suggestionText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  removeSuggestion: {
    padding: 4,
  },
  selectionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectionCount: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  selectionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  deleteButton: {
    backgroundColor: '#f44336',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  flatList: {
    flex: 1,
  },
  listContent: {
    padding: 16,
    paddingTop: 0,
  },
  conversationCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedCard: {
    borderColor: '#4CAF50',
    borderWidth: 2,
  },
  archivedCard: {
    opacity: 0.7,
    backgroundColor: '#f5f5f5',
  },
  selectionIndicator: {
    marginRight: 12,
  },
  conversationContent: {
    flex: 1,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  conversationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    marginRight: 12,
  },
  conversationMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  archiveIcon: {
    marginRight: 4,
  },
  conversationDate: {
    fontSize: 12,
    color: '#666',
  },
  conversationPreview: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  conversationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageCount: {
    fontSize: 12,
    color: '#2196F3',
    fontWeight: '500',
  },
  conversationTime: {
    fontSize: 12,
    color: '#999',
  },
  moreButton: {
    padding: 8,
    marginLeft: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  // Swipeable styles
  swipeableContainer: {
    marginBottom: 12,
  },
  actionButtons: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 16,
  },
  swipeActionButton: {
    width: 60,
    height: '80%',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    borderRadius: 8,
  },
  swipeArchiveButton: {
    backgroundColor: '#FF9800',
  },
  swipeDeleteButton: {
    backgroundColor: '#F44336',
  },
  swipeActionButtonText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
    marginTop: 2,
  },
  activeFiltersContainer: {
    marginHorizontal: 16,
    marginBottom: 12,
  },
  activeFiltersContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f8ff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e3f2fd',
  },
  activeFiltersText: {
    fontSize: 12,
    color: '#2196F3',
    fontWeight: '500',
    marginLeft: 6,
    flex: 1,
  },
  clearFiltersButton: {
    padding: 4,
  },
});
