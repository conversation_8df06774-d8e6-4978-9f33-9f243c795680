import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  Alert,
  TouchableOpacity,
  Text,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ChatScreen } from './components/health/ChatScreen';
import { useLocalization } from './context/LocalizationContext';
import { getHealthConversation } from './services/HealthConsultationSupabase';
import { HealthConversation } from './types/healthConsultation';

export default function HealthChatScreen() {
  const { conversationId } = useLocalSearchParams<{ conversationId: string }>();
  const router = useRouter();
  const { t } = useLocalization();

  // 🎯 LOCAL STATE - No more Context dependency for individual conversation
  const [conversation, setConversation] = useState<HealthConversation | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 🎯 LOAD CONVERSATION - Direct service call
  useEffect(() => {
    const loadConversationData = async () => {
      if (!conversationId) {
        setError('No conversation ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const conversationData = await getHealthConversation(conversationId);
        if (!conversationData) {
          setError('Conversation not found');
        } else {
          setConversation(conversationData);
        }
      } catch (err) {
        console.error('Failed to load conversation:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to load conversation'
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadConversationData();
  }, [conversationId]);

  const handleGoBack = () => {
    router.back();
  };

  const handleConversationOptions = () => {
    if (!conversation) return;

    Alert.alert(t('healthConsultation.conversationOptions.title'), undefined, [
      {
        text: t('healthConsultation.conversationOptions.shareConversation'),
        onPress: () => {
          // TODO: Implement conversation sharing
          console.log('Share conversation:', conversation.id);
        },
      },
      {
        text: t('healthConsultation.conversationOptions.deleteConversation'),
        style: 'destructive',
        onPress: () => {
          Alert.alert(
            t('healthConsultation.confirmDelete.title'),
            t('healthConsultation.confirmDelete.message'),
            [
              {
                text: t('common.cancel'),
                style: 'cancel',
              },
              {
                text: t('common.delete'),
                style: 'destructive',
                onPress: async () => {
                  try {
                    // TODO: Implement conversation deletion
                    console.log('Delete conversation:', activeConversation.id);
                    router.back();
                  } catch (error) {
                    console.error('Failed to delete conversation:', error);
                  }
                },
              },
            ]
          );
        },
      },
      {
        text: t('common.cancel'),
        style: 'cancel',
      },
    ]);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleGoBack}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color="#2196F3" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {t('healthConsultation.loading')}
          </Text>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>
            {t('healthConsultation.loadingConversation')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !conversation) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleGoBack}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color="#2196F3" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {t('healthConsultation.error.title')}
          </Text>
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#FF5722" />
          <Text style={styles.errorText}>
            {error || t('healthConsultation.error.conversationNotFound')}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleGoBack}>
            <Text style={styles.retryButtonText}>{t('common.goBack')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleGoBack}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color="#2196F3" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle} numberOfLines={1}>
            {conversation.title}
          </Text>
          <Text style={styles.headerSubtitle}>
            {t('healthConsultation.aiDoctor')}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.optionsButton}
          onPress={handleConversationOptions}
          activeOpacity={0.7}
        >
          <Ionicons name="ellipsis-vertical" size={24} color="#2196F3" />
        </TouchableOpacity>
      </View>

      {/* Chat Screen */}
      <ChatScreen conversationId={conversationId!} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerContent: {
    flex: 1,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  optionsButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
