import 'react-native-get-random-values'; // Must be imported before any UUID usage
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StyleSheet } from 'react-native';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { useEffect, useState } from 'react';
import { initializeRevenueCat } from './services/revenueCatService';
import {
  trackAppLaunch,
  shouldShowRatingModal,
  markRatingModalShown,
  showAppStoreReviewIfEligible,
} from './services/userEngagementService';
import { RatingModal } from './components/RatingModal';

import { LocalizationProvider } from './context/LocalizationContext';

import { DrawerProvider } from './context/DrawerContext';
import { FilterProvider } from './context/FilterContext';
import { FavoritesProvider } from './context/FavoritesContext';
import { AvoidedAdditivesProvider } from './context/AvoidedAdditivesContext';
import { CustomLabelsProvider } from './context/CustomLabelsContext';
import { CommunityContributionsProvider } from './context/CommunityContributionsContext';
import { ToastProvider } from './context/ToastContext';
import { AdditivesProvider } from './context/AdditivesContext';

import { SubscriptionProvider } from './context/SubscriptionContext';

export default function RootLayout() {
  const [showRatingModal, setShowRatingModal] = useState(false);

  // Initialize RevenueCat on app start and get unified device ID
  useEffect(() => {
    const initializeApp = async () => {
      try {
        const unifiedDeviceId = await initializeRevenueCat();
        console.log(
          '🚀 App initialized with unified device ID:',
          unifiedDeviceId
        );

        // Track app launch and check if rating modal should be shown
        await trackAppLaunch();

        // Small delay to ensure app is fully loaded
        setTimeout(async () => {
          const shouldShow = await shouldShowRatingModal();
          if (shouldShow) {
            setShowRatingModal(true);
          }
        }, 3000); // 3 second delay for better UX
      } catch (error) {
        console.error('❌ Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, []);

  const handleRatingSubmit = async (rating: number) => {
    setShowRatingModal(false);
    await markRatingModalShown(rating);

    // Show App Store review if rating is 4 or higher
    setTimeout(async () => {
      await showAppStoreReviewIfEligible(rating);
    }, 500); // Small delay for better UX
  };

  const handleRatingClose = async () => {
    setShowRatingModal(false);
    await markRatingModalShown(); // Mark as shown without rating
  };

  // const { t } = useLocalization();
  return (
    <LocalizationProvider>
      <AdditivesProvider>
        <DrawerProvider>
          <FilterProvider>
            <FavoritesProvider>
              <AvoidedAdditivesProvider>
                <CustomLabelsProvider>
                  <CommunityContributionsProvider>
                    <SubscriptionProvider>
                      <ToastProvider>
                        <GestureHandlerRootView style={styles.container}>
                          <BottomSheetModalProvider>
                            <StatusBar style="auto" />
                            <Stack screenOptions={{ headerShown: false }}>
                              <Stack.Screen
                                name="(tabs)"
                                options={{
                                  headerShown: false,
                                  title: 'E-Code Checker',
                                }}
                              />
                              <Stack.Screen
                                name="favorites"
                                options={{
                                  headerShown: true,
                                }}
                              />
                              <Stack.Screen
                                name="avoided"
                                options={{
                                  headerShown: true,
                                }}
                              />
                              <Stack.Screen
                                name="labels"
                                options={{
                                  headerShown: true,
                                }}
                              />
                              <Stack.Screen
                                name="health-chat"
                                options={{
                                  headerShown: false,
                                }}
                              />
                              <Stack.Screen
                                name="health-history"
                                options={{
                                  headerShown: true,
                                }}
                              />
                              <Stack.Screen
                                name="health-conversation-detail"
                                options={{
                                  headerShown: true,
                                }}
                              />

                              {/* Modal Screens */}
                              <Stack.Screen
                                name="add-product"
                                options={{
                                  presentation: 'modal',
                                  headerShown: true,
                                }}
                              />
                              <Stack.Screen
                                name="report"
                                options={{
                                  presentation: 'modal',
                                  headerShown: true,
                                }}
                              />
                              <Stack.Screen
                                name="paywall"
                                options={{
                                  presentation: 'modal',
                                  headerShown: false,
                                }}
                              />

                              {/* Modal Stack */}
                              <Stack.Screen
                                name="modal-stack"
                                options={{
                                  presentation: 'modal',
                                  headerShown: false,
                                }}
                              />
                            </Stack>
                          </BottomSheetModalProvider>
                        </GestureHandlerRootView>
                      </ToastProvider>
                    </SubscriptionProvider>
                  </CommunityContributionsProvider>
                </CustomLabelsProvider>
              </AvoidedAdditivesProvider>
            </FavoritesProvider>
          </FilterProvider>
        </DrawerProvider>
      </AdditivesProvider>

      {/* Rating Modal */}
      <RatingModal
        visible={showRatingModal}
        onClose={handleRatingClose}
        onRating={handleRatingSubmit}
      />
    </LocalizationProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
