{"name": "bolt-expo-starter", "main": "expo-router/entry", "version": "1.0.1", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "android": "expo run:android", "ios": "expo run:ios", "start": "expo start --dev-client"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.1.6", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/datetimepicker": "8.2.0", "@react-native-menu/menu": "^1.2.3", "@react-native-ml-kit/text-recognition": "^1.5.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.49.4", "axios": "^1.9.0", "expo": "~52.0.47", "expo-blur": "^14.0.3", "expo-camera": "~16.0.18", "expo-constants": "~17.0.8", "expo-device": "^7.0.3", "expo-font": "^13.0.3", "expo-haptics": "^14.0.1", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "^7.0.5", "expo-media-library": "~17.0.6", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.24", "expo-status-bar": "^2.0.1", "expo-store-review": "^8.1.5", "expo-symbols": "^0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "^14.0.2", "fuse.js": "^7.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-circular-progress": "^1.4.1", "react-native-executorch": "^0.3.2", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-markdown-display": "^7.0.2", "react-native-purchases": "^8.11.8", "react-native-purchases-ui": "^8.11.8", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.19.13", "react-native-webview": "13.12.5", "use-debounce": "^10.0.4", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "@types/uuid": "^10.0.0", "babel-plugin-react-compiler": "^19.0.0-beta-af1b7da-20250417", "eslint": "^8.57.1", "eslint-config-expo": "~8.0.1", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "typescript": "^5.3.3"}}